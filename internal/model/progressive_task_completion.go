package model

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"net"
	"time"

	"github.com/google/uuid"
)

// MilestoneData represents milestone-specific data for progressive tasks
type MilestoneData struct {
	Level           int                    `json:"level"`
	LevelName       string                 `json:"level_name,omitempty"`
	RequiredValue   int                    `json:"required_value"`
	AchievedValue   int                    `json:"achieved_value"`
	BonusMultiplier *float64               `json:"bonus_multiplier,omitempty"`
	CustomData      map[string]interface{} `json:"custom_data,omitempty"`
}

// Value implements the driver.Valuer interface for MilestoneData
func (md MilestoneData) Value() (driver.Value, error) {
	return json.Marshal(md)
}

// <PERSON><PERSON> implements the sql.Scanner interface for MilestoneData
func (md *MilestoneData) Scan(value interface{}) error {
	if value == nil {
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}

	return json.Unmarshal(bytes, md)
}

// ProgressiveTaskCompletion represents the progressive_task_completions table
type ProgressiveTaskCompletion struct {
	ID               uuid.UUID         `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	UserID           uuid.UUID         `gorm:"type:uuid;not null;index:idx_progressive_completions_user_task;uniqueIndex:uk_progressive_user_task_level" json:"user_id"`
	TaskID           uuid.UUID         `gorm:"type:uuid;not null;index:idx_progressive_completions_user_task;uniqueIndex:uk_progressive_user_task_level" json:"task_id"`
	LevelCompleted   int               `gorm:"not null;index:idx_progressive_completions_level;uniqueIndex:uk_progressive_user_task_level" json:"level_completed"`
	TotalProgress    int               `gorm:"not null;default:0" json:"total_progress"`
	PointsAwarded    int               `gorm:"not null;default:0" json:"points_awarded"`
	CompletionDate   time.Time         `gorm:"not null;default:CURRENT_TIMESTAMP;index:idx_progressive_completions_date" json:"completion_date"`
	VerificationData *VerificationData `gorm:"type:jsonb" json:"verification_data"`
	MilestoneData    *MilestoneData    `gorm:"type:jsonb" json:"milestone_data"`
	IPAddress        *net.IP           `gorm:"type:inet" json:"ip_address"`
	UserAgent        *string           `gorm:"type:text" json:"user_agent"`
	CreatedAt        time.Time         `gorm:"default:CURRENT_TIMESTAMP" json:"created_at"`

	// Relationships
	User User         `gorm:"foreignKey:UserID;references:ID" json:"user,omitempty"`
	Task ActivityTask `gorm:"foreignKey:TaskID;references:ID" json:"task,omitempty"`
}

// TableName specifies the table name for ProgressiveTaskCompletion
func (ProgressiveTaskCompletion) TableName() string {
	return "progressive_task_completions"
}

// IsVerified checks if the task completion was verified
func (ptc *ProgressiveTaskCompletion) IsVerified() bool {
	return ptc.VerificationData != nil && ptc.VerificationData.VerificationMethod != ""
}

// GetVerificationMethod returns the verification method used
func (ptc *ProgressiveTaskCompletion) GetVerificationMethod() string {
	if ptc.VerificationData == nil {
		return ""
	}
	return ptc.VerificationData.VerificationMethod
}

// SetVerificationData sets the verification data for the completion
func (ptc *ProgressiveTaskCompletion) SetVerificationData(method, source string, customData map[string]interface{}) {
	ptc.VerificationData = &VerificationData{
		VerificationMethod: method,
		VerifiedAt:         time.Now(),
		VerificationSource: source,
		CustomData:         customData,
	}
}

// SetMilestoneData sets the milestone data for the completion
func (ptc *ProgressiveTaskCompletion) SetMilestoneData(level int, levelName string, requiredValue, achievedValue int, bonusMultiplier *float64, customData map[string]interface{}) {
	ptc.MilestoneData = &MilestoneData{
		Level:           level,
		LevelName:       levelName,
		RequiredValue:   requiredValue,
		AchievedValue:   achievedValue,
		BonusMultiplier: bonusMultiplier,
		CustomData:      customData,
	}
}

// ToTaskCompletionHistory converts ProgressiveTaskCompletion to TaskCompletionHistory for backward compatibility
func (ptc *ProgressiveTaskCompletion) ToTaskCompletionHistory() *TaskCompletionHistory {
	return &TaskCompletionHistory{
		ID:               ptc.ID,
		UserID:           ptc.UserID,
		TaskID:           ptc.TaskID,
		PointsAwarded:    ptc.PointsAwarded,
		CompletionDate:   ptc.CompletionDate,
		VerificationData: ptc.VerificationData,
		IPAddress:        ptc.IPAddress,
		UserAgent:        ptc.UserAgent,
		CreatedAt:        ptc.CreatedAt,
		User:             ptc.User,
		Task:             ptc.Task,
	}
}

// FromTaskCompletionHistory creates ProgressiveTaskCompletion from TaskCompletionHistory
func (ptc *ProgressiveTaskCompletion) FromTaskCompletionHistory(tch *TaskCompletionHistory, levelCompleted, totalProgress int) {
	ptc.ID = tch.ID
	ptc.UserID = tch.UserID
	ptc.TaskID = tch.TaskID
	ptc.LevelCompleted = levelCompleted
	ptc.TotalProgress = totalProgress
	ptc.PointsAwarded = tch.PointsAwarded
	ptc.CompletionDate = tch.CompletionDate
	ptc.VerificationData = tch.VerificationData
	ptc.IPAddress = tch.IPAddress
	ptc.UserAgent = tch.UserAgent
	ptc.CreatedAt = tch.CreatedAt
	ptc.User = tch.User
	ptc.Task = tch.Task
}
