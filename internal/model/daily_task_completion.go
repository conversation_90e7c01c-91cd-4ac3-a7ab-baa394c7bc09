package model

import (
	"net"
	"time"

	"github.com/google/uuid"
)

// DailyTaskCompletion represents the daily_task_completions table
type DailyTaskCompletion struct {
	ID               uuid.UUID         `gorm:"type:uuid;primary_key;default:gen_random_uuid()" json:"id"`
	UserID           uuid.UUID         `gorm:"type:uuid;not null;index:idx_daily_completions_user_date" json:"user_id"`
	TaskID           uuid.UUID         `gorm:"type:uuid;not null;index:idx_daily_completions_task_date" json:"task_id"`
	PointsAwarded    int               `gorm:"not null;default:0" json:"points_awarded"`
	CompletionDate   time.Time         `gorm:"type:date;not null;default:CURRENT_DATE;index:idx_daily_completions_date" json:"completion_date"`
	CompletionTime   time.Time         `gorm:"not null;default:CURRENT_TIMESTAMP" json:"completion_time"`
	VerificationData *VerificationData `gorm:"type:jsonb" json:"verification_data"`
	IPAddress        *net.IP           `gorm:"type:inet" json:"ip_address"`
	UserAgent        *string           `gorm:"type:text" json:"user_agent"`
	CreatedAt        time.Time         `gorm:"default:CURRENT_TIMESTAMP" json:"created_at"`

	// Relationships
	User User         `gorm:"foreignKey:UserID;references:ID" json:"user,omitempty"`
	Task ActivityTask `gorm:"foreignKey:TaskID;references:ID" json:"task,omitempty"`
}

// TableName specifies the table name for DailyTaskCompletion
func (DailyTaskCompletion) TableName() string {
	return "daily_task_completions"
}

// IsVerified checks if the task completion was verified
func (dtc *DailyTaskCompletion) IsVerified() bool {
	return dtc.VerificationData != nil && dtc.VerificationData.VerificationMethod != ""
}

// GetVerificationMethod returns the verification method used
func (dtc *DailyTaskCompletion) GetVerificationMethod() string {
	if dtc.VerificationData == nil {
		return ""
	}
	return dtc.VerificationData.VerificationMethod
}

// SetVerificationData sets the verification data for the completion
func (dtc *DailyTaskCompletion) SetVerificationData(method, source string, customData map[string]interface{}) {
	dtc.VerificationData = &VerificationData{
		VerificationMethod: method,
		VerifiedAt:         time.Now(),
		VerificationSource: source,
		CustomData:         customData,
	}
}

// ToTaskCompletionHistory converts DailyTaskCompletion to TaskCompletionHistory for backward compatibility
func (dtc *DailyTaskCompletion) ToTaskCompletionHistory() *TaskCompletionHistory {
	return &TaskCompletionHistory{
		ID:               dtc.ID,
		UserID:           dtc.UserID,
		TaskID:           dtc.TaskID,
		PointsAwarded:    dtc.PointsAwarded,
		CompletionDate:   dtc.CompletionTime,
		VerificationData: dtc.VerificationData,
		IPAddress:        dtc.IPAddress,
		UserAgent:        dtc.UserAgent,
		CreatedAt:        dtc.CreatedAt,
		User:             dtc.User,
		Task:             dtc.Task,
	}
}

// FromTaskCompletionHistory creates DailyTaskCompletion from TaskCompletionHistory
func (dtc *DailyTaskCompletion) FromTaskCompletionHistory(tch *TaskCompletionHistory) {
	dtc.ID = tch.ID
	dtc.UserID = tch.UserID
	dtc.TaskID = tch.TaskID
	dtc.PointsAwarded = tch.PointsAwarded
	dtc.CompletionDate = time.Date(tch.CompletionDate.Year(), tch.CompletionDate.Month(), tch.CompletionDate.Day(), 0, 0, 0, 0, tch.CompletionDate.Location())
	dtc.CompletionTime = tch.CompletionDate
	dtc.VerificationData = tch.VerificationData
	dtc.IPAddress = tch.IPAddress
	dtc.UserAgent = tch.UserAgent
	dtc.CreatedAt = tch.CreatedAt
	dtc.User = tch.User
	dtc.Task = tch.Task
}
